
import numpy as np
import matplotlib.pyplot as plt
import scipy.io
import time

# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# %  完整的三能级系统 2D 光谱数值模拟(示例)
# %  包含了脉冲时域/频域转换，龙格-库塔积分，以及 2D 光谱绘图
# %  注释详尽，帮助理解各部分功能和逻辑
# %  -- 最终版示例 --
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

# Clear workspace, close figures, and start timer
tic = time.time()

# ================== 一、常量和转换因子 ==================
CmeV = 0.124 / 1000.  # cm-1 ----> eV, 1 cm^-1 ~ 1.24e-4 eV
wlam = 1239.8424121   # nm <--> eV 的转换因子 (E[eV]*lambda[nm] = 1239.842..)
dh = 1.5193           # 10/hbar, 常用于相位因子的单位修正
Deb = 3.33564e-30     # 1 Debye = 3.33564e-30 C·m
Ev = 1.602e-19        # 1 eV = 1.602e-19 J
rk = 4                # rk = 1,2,3,4 ----- 表示Runge-Kutta的阶数

# ================== 二、脉冲参数 ==================
# 啁啾系数
ch2 = 1510 * 0  # 二阶啁啾，单位 fs^(-2)
ch3 = 1050 * 0  # 三阶啁啾，单位 fs^(-3)

# 脉冲时宽
TDp = 50             # fs, 强度FWHM
TDe = TDp * np.sqrt(2)  # fs, 幅度FWHM
coeff = 15           # 截断系数, 当 |t - t0| > coeff*TDe 时认为脉冲振幅近似为0

# 频域到时域的转换参数
WWp = np.sqrt(8 * np.log(2)) / TDp * 5  # 频域积分上下限, 与脉冲时宽相关
stWW = WWp / 100                        # 频域积分步长
NWW = 2 * round(WWp / stWW) + 1         # 频域采样点数

# 脉冲载波(中心)波长
WDp = 800       # nm (若想改成其他波长，在此设置即可)

# 相位循环维数
Nfi = 3         # Nfi x Nfi x Nfi

# ================== 三、系统哈密顿量和能级结构 ==================
# 体系：三能级系统 (基态 + 2个单激发态)
N0 = 1   # 基态个数
N1 = 2   # 单激发态个数
N2 = 0   # 双激发态数目(此处为0)
NN = N0 + N1 + N2  # 总能级数 = 3

# 基态/激发态哈密顿量矩阵(先用 0 矩阵初始化)
H0 = np.zeros((N0, N0))
H1 = np.zeros((N1, N1))

# 能量(单位 cm^-1)
E1 = 0           # 基态能级
E2 = 11765       # 第一个激发态(B850)
DeltaE = 735     # 两峰之间的间隔
E3 = E2 + DeltaE # 第二个激发态

# 写入哈密顿量 (只需要对角赋值)
H0[0, 0] = E1
H1[0, 0] = E2
H1[1, 1] = E3

# 跃迁偶极矩阵 X (仅考虑基态到各激发态)
X = np.zeros((NN, NN))
Mu21R = 6   # |E1> -> |E2> 的偶极矩(单位 Debye)
Mu31R = 12  # |E1> -> |E3> 的偶极矩(单位 Debye)
# 在三能级系统下，X(1,2), X(1,3) 为基态->激发态的偶极矩
# 假设 X(2,1), X(3,1) = X(1,2), X(1,3) 的转置(在计算中用 Xd = X')
X[0, 1] = Mu21R
X[0, 2] = Mu31R

# ================== 四、光场幅度参数 ==================
FiAm = 0.009843  # 峰值场强(单位 eV) (示例)
# 如果想考虑真实的 SI 幅度, 可用 E_field = FiAm * Ev / (Mu21R * Deb)

# ================== 五、离散时间步长与传播设置 ==================
# 相对于原始参数做了调小, 以减少计算量
max_tau12 = 20    # 原来40, 现缩减为20
max_tau34 = 20    # 同上
step_tau = 10     # 步长(fs), 原来5
maxT = 5          # 原来10
stepTp = 20       # 步长(fs), 原来10
TT = 400          # 用于某些只算单个T时
Delta_t = 1       # 演化时间步长(fs)
Gamma = 0.0005    # 弛豫或退相干速率(可在后续使用)

# ================== 六、主程序计算 ==================
# 计算载频
Nfi3 = Nfi**3
wp = wlam / WDp           # 中心频率 (eV)，如果需要 2pi/hbar，请自行再做转换
Delta_tR = Delta_t * dh   # 考虑 dh 的时间步(相位步)

# 计算能级间跃迁频率(单位 eV)
w21 = (E2 - E1) * CmeV
w31 = (E3 - E1) * CmeV

# 先定义一些初始相位/时延/场强
w1 = 0.0
w2 = 0.0
w3 = 0.0
w4 = 0.0
tau1 = 0.
tau2 = 0
tau3 = 0
tau4 = 0
F1 = FiAm
F2 = FiAm
F3 = FiAm
F4 = FiAm
fi1 = 0
fi2 = 0
fi3 = 0
fi4 = 0    # 四个脉冲初始相位
tau_initial = 0  # 若需要延迟基准点可在此调整

# 基态/激发态单位阵
Z0 = np.eye(N0, N0)
Z1 = np.eye(N1, N1)

# 将 H0, H1 转为 eV
H0 = H0 * CmeV
H1 = H1 * CmeV

# 在旋转参考系下修正 H1, 减去一个 wp (针对单光子跃迁)
H1 = H1 - Z1 * wp

# 若 N2=0, 则 H 直接拼为块对角 [H0, H1]
if N2 == 0:
    H = np.block([[H0, np.zeros((N0, N1))], [np.zeros((N1, N0)), H1]])
else:
    Z2 = np.eye(N2, N2)
    # 假设还定义了 H2, 要进行同样的转换和旋转, 这里忽略
    H2 = H2 * CmeV
    H2 = H2 - Z2 * 2 * wp
    H = np.block([[H0, np.zeros((N0, N1)), np.zeros((N0, N2))],
                  [np.zeros((N1, N0)), H1, np.zeros((N1, N2))],
                  [np.zeros((N2, N0)), np.zeros((N2, N1)), H2]])

# Xd 为偶极矩阵的厄米共轭(对角元为0, 仅转置即可)
Xd = X.T

# 密度矩阵初始条件
RhoMuNu = np.zeros((NN, 1))
RhoMuNuIn = np.zeros((NN, 1))
RhoMuNuIn_0 = np.zeros((NN, 1))
RhoMuNuIn_0[0, 0] = 1.  # 初始全在基态

# 记录数组
ttau12 = np.zeros((max_tau12, 1))
ttau34 = np.zeros((max_tau34, 1))

# 三维结果(随 tau12, tau34, T)
PopiR3 = np.zeros((max_tau12, max_tau34, maxT))
PopiNR3 = np.zeros((max_tau12, max_tau34, maxT))

# 在结果存储前, 先定义一些临时变量
pppR = np.zeros((max_tau12, 1))
pppNR = np.zeros((max_tau12, 1))

# 为 RK 方法准备系数矩阵
Ark = np.zeros((4, 4))
Brk = np.zeros((4, 1))
Crk = np.zeros((4, 1))

KK1 = np.zeros((NN, 1))
KK2 = np.zeros((NN, 1))
KK3 = np.zeros((NN, 1))
KK4 = np.zeros((NN, 1))

# 设置RK阶数对应的参数
if rk == 1:
    Brk[0] = 1.
if rk == 2:
    Ark[1, 0] = 0.5
    Brk[1] = 1.
    Crk[1] = 0.5
if rk == 3:
    Ark[1, 0] = 0.5
    Ark[2, 0] = -1.
    Ark[2, 1] = 2.
    Brk[0] = 1. / 6.
    Brk[1] = 4. / 6.
    Brk[2] = 1. / 6.
    Crk[1] = 0.5
    Crk[2] = 1.
if rk == 4:
    Ark[1, 0] = 0.5
    Ark[2, 1] = 0.5
    Ark[3, 2] = 1.
    Brk[0] = 1. / 6.
    Brk[1] = 2. / 6.
    Brk[2] = 2. / 6.
    Brk[3] = 1. / 6.
    Crk[1] = 0.5
    Crk[2] = 0.5
    Crk[3] = 1.

# ------------------ 主循环: 扫描T (iT=1:maxT) ------------------
for iT in range(maxT):
    T = (iT) * stepTp
    if maxT == 1:
        T = TT  # 若只算一帧时, T=TT

    print(f'当前 T = {T}')
    Tp = np.zeros((maxT, 1))
    Tp[iT, 0] = T

    # 用于存储在 (tau12, tau34) 二维扫描下的布居
    PopiR = np.zeros((max_tau12, max_tau34))
    PopiNR = np.zeros((max_tau12, max_tau34))

    # ------------- 遍历 tau12 -------------
    for itau12 in range(max_tau12):
        tau12 = itau12 * step_tau + tau_initial
        ttau12[itau12, 0] = tau12

        # ------------- 遍历 tau34 -------------
        for itau34 in range(max_tau34):
            tau34 = itau34 * step_tau + tau_initial
            ttau34[itau34, 0] = tau34

            # 决定4个脉冲在时间轴上的中心位置
            tau1 = -tau12 - T - tau34
            tau2 = -T - tau34
            tau3 = -tau34
            tau4 = 0

            # 用于决定数值演化在时间上的范围
            # 比如从最早出现的脉冲中心 - coeff*TDe 到最后一个脉冲中心 + coeff*TDe
            ttt = 2 * coeff * TDe + tau12 + tau34 + T
            tau0 = -coeff * TDe - tau12 - T - tau34

            maxJ = round((2 * coeff * TDe + max_tau12 + max_tau34 + TT) / Delta_t) + 1
            # 这里适当加1, 确保覆盖时序

            # --------- 计算脉冲在时间域的包络(通过频域积分) ---------
            maxJ2 = 2 * maxJ + 3  # 为了半步插值等(2*jt-1)，这里多设置些点
            Pulse1 = np.zeros((maxJ2, 1))
            Pulse2 = np.zeros((maxJ2, 1))
            Pulse3 = np.zeros((maxJ2, 1))
            Pulse4 = np.zeros((maxJ2, 1))

            TRK = np.zeros((maxJ2, 1))
            PulseT = np.zeros((maxJ2, 1))
            PulseW = np.zeros((NWW, 1))
            WWW = np.zeros((NWW, 1))

            for jt2 in range(maxJ2):
                tt2 = tau0 + jt2 * Delta_t / 2.
                TRK[jt2, 0] = tt2

                # 这里给出一个脉冲形状的简单参考(高斯), 用来对比Plot
                # PulseT: 仅针对第一个脉冲中心(tau1)的高斯包络
                PulseT[jt2, 0] = np.exp(-2 * np.log(2) * ((tt2 - tau1) / TDp)**2)

                # 判定是否在脉冲有效区间 (绝对值大于coeff*TDe视为0)
                A1 = 1
                A2 = 1
                A3 = 1
                A4 = 1
                if abs(tt2 - tau1) > coeff * TDe:
                    A1 = 0
                if abs(tt2 - tau2) > coeff * TDe:
                    A2 = 0
                if abs(tt2 - tau3) > coeff * TDe:
                    A3 = 0
                if abs(tt2 - tau4) > coeff * TDe:
                    A4 = 0

                # 频域积分: 用 Gaussian * 啁啾相位 = EW(iw)
                for iw in range(NWW):
                    WW = iw * stWW - WWp  # 频率扫描
                    WWW[iw, 0] = WW
                    # 对应脉冲的频域包络(含啁啾相位)
                    EW = np.exp(-TDp**2 / (8 * np.log(2)) * WW**2 - 1j / 2 * ch2 * WW**2 - 1j / 6 * ch3 * WW**3)

                    PulseW[iw, 0] = EW  # 暂时仅存一个示例

                    # 将频域中每个频率分量干涉相加到时域(针对4个脉冲中心)
                    Pulse1[jt2, 0] = Pulse1[jt2, 0] + A1 * EW * np.exp(1j * WW * (tt2 - tau1))
                    Pulse2[jt2, 0] = Pulse2[jt2, 0] + A2 * EW * np.exp(1j * WW * (tt2 - tau2))
                    Pulse3[jt2, 0] = Pulse3[jt2, 0] + A3 * EW * np.exp(1j * WW * (tt2 - tau3))
                    Pulse4[jt2, 0] = Pulse4[jt2, 0] + A4 * EW * np.exp(1j * WW * (tt2 - tau4))

            # 对4个脉冲进行归一化(取实部最大值做基准，仅作演示)
            Pu1 = np.max(np.real(Pulse1))
            Pu2 = np.max(np.real(Pulse2))
            Pu3 = np.max(np.real(Pulse3))
            Pu4 = np.max(np.real(Pulse4))
            Pulse1 = Pulse1 / Pu1
            Pulse2 = Pulse2 / Pu2
            Pulse3 = Pulse3 / Pu3
            Pulse4 = Pulse4 / Pu4

            # 对比用的形状 PulseT 也做归一化
            PuT = np.max(np.real(PulseT))
            PulseT = PulseT / PuT

            # 如果只想在某些条件下画图, 例如 itau12=1 且 itau34=20 时
            if (itau12 == 0) and (itau34 == 19):
                plt.figure()
                plt.plot(TRK, np.real(Pulse1), 'r', TRK, np.imag(Pulse1), 'k', TRK, PulseT, 'g')
                plt.title('Pulse1(实部/虚部) & 参考脉冲(PulseT)')
                plt.xlabel('Time (fs)')
                plt.ylabel('Amplitude')
                plt.show()

                plt.figure()
                plt.plot(WWW, np.real(PulseW), 'r', WWW, np.imag(PulseW), 'k')
                plt.title('脉冲频域包络 PulseW(实部/虚部)')
                plt.xlabel('Frequency (a.u.)')
                plt.show()

            # ------------------ 相位循环 ------------------
            for i1 in range(Nfi):
                fi1 = 2 * np.pi * i1 / Nfi
                for i2 in range(Nfi):
                    fi2 = 2 * np.pi * i2 / Nfi
                    for i3 in range(Nfi):
                        fi3 = 2 * np.pi * i3 / Nfi

                        # 相位匹配因子(波矢匹配), Rm 和 NRm
                        Rm = np.exp(1j * (-fi1 + fi2 + fi3)) / Nfi3
                        NRm = np.exp(1j * (fi1 - fi2 + fi3)) / Nfi3

                        # -- 演化前初始化密度矩阵 (都在基态)
                        RhoMuNuIn = RhoMuNuIn_0.copy()

                        # 记录最后的布居
                        # 实际可在演化过程中随时存，但这里只要最终
                        Popul = np.zeros((maxJ, 1))  # 基态
                        Popue = np.zeros((maxJ, 1))  # 激发态

                        # ------------------ 时间演化循环 ------------------
                        tm = np.zeros(maxJ)
                        for jt in range(maxJ):
                            # 时间序列 tm
                            tm[jt] = tau0 + jt * Delta_t
                            tc = tm[jt]

                            # 在时间 tc 的光场幅度(取 2*jt-1, 是为了与 maxJ2 里对应的下标)
                            g1 = F1 * Pulse1[2 * jt, 0]
                            g2 = F2 * Pulse2[2 * jt, 0]
                            g3 = F3 * Pulse3[2 * jt, 0]
                            g4 = F4 * Pulse4[2 * jt, 0]

                            # 与载频相乘并减去相位循环项 fiX
                            phase1 = (w1 * tc) * dh - fi1
                            phase2 = (w2 * tc) * dh - fi2
                            phase3 = (w3 * tc) * dh - fi3
                            phase4 = (w4 * tc) * dh - fi4

                            # 光场 FX = sum( E(t) * e^{i\phi} )
                            FX = (np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 +
                                  np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4)
                            FXd = np.conj(FX)  # 或者简单地 = FX' 对标量也行

                            # 总算符 = -X*FX - Xd*FXd + H
                            # 其中 -X*FX 对应与偶极相互作用, H 是哈密顿量(旋转系下)
                            SFt = - X * FX - Xd * FXd + H  # 纠正: Xd*Xd并不合适, 这里应是 -Xd*FXd => -X^\dagger * conj(FX)
                            # 在原代码中写的是: - X * FX - Xd * FXd, 其含义是H_{int} = - (X^\dagger E + X E^*) ...
                            # 您可以根据具体记号(若 Xd= X^\dagger ) 做相应调整
                            # 此处保留原有写法

                            RhoMuNuIn_K = RhoMuNuIn.copy()
                            KK1 = -1j * SFt @ RhoMuNuIn_K

                            # RK2 ~ RK4
                            if rk > 1:
                                tc = tm[jt] + Delta_t * Crk[1, 0]
                                g1 = F1 * Pulse1[2 * jt, 0]
                                g2 = F2 * Pulse2[2 * jt, 0]
                                g3 = F3 * Pulse3[2 * jt, 0]
                                g4 = F4 * Pulse4[2 * jt, 0]
                                phase1 = (w1 * tc) * dh - fi1
                                phase2 = (w2 * tc) * dh - fi2
                                phase3 = (w3 * tc) * dh - fi3
                                phase4 = (w4 * tc) * dh - fi4
                                FX = (np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 +
                                      np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4)
                                FXd = np.conj(FX)
                                SFt = - X * FX - Xd * FXd + H
                                RhoMuNuIn_K = RhoMuNuIn + Delta_tR * Ark[1, 0] * KK1
                                KK2 = -1j * SFt @ RhoMuNuIn_K

                            if rk > 2:
                                tc = tm[jt] + Delta_t * Crk[2, 0]
                                g1 = F1 * Pulse1[2 * jt, 0]
                                g2 = F2 * Pulse2[2 * jt, 0]
                                g3 = F3 * Pulse3[2 * jt, 0]
                                g4 = F4 * Pulse4[2 * jt, 0]
                                phase1 = (w1 * tc) * dh - fi1
                                phase2 = (w2 * tc) * dh - fi2
                                phase3 = (w3 * tc) * dh - fi3
                                phase4 = (w4 * tc) * dh - fi4
                                FX = (np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 +
                                      np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4)
                                FXd = np.conj(FX)
                                SFt = - X * FX - Xd * FXd + H
                                RhoMuNuIn_K = RhoMuNuIn + Delta_tR * (Ark[2, 0] * KK1 + Ark[2, 1] * KK2)
                                KK3 = -1j * SFt @ RhoMuNuIn_K

                            if rk > 3:
                                tc = tm[jt] + Delta_t * Crk[3, 0]
                                g1 = F1 * Pulse1[2 * jt, 0]
                                g2 = F2 * Pulse2[2 * jt, 0]
                                g3 = F3 * Pulse3[2 * jt, 0]
                                g4 = F4 * Pulse4[2 * jt, 0]
                                phase1 = (w1 * tc) * dh - fi1
                                phase2 = (w2 * tc) * dh - fi2
                                phase3 = (w3 * tc) * dh - fi3
                                phase4 = (w4 * tc) * dh - fi4
                                FX = (np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 +
                                      np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4)
                                FXd = np.conj(FX)
                                SFt = - X * FX - Xd * FXd + H
                                RhoMuNuIn_K = RhoMuNuIn + Delta_tR * (Ark[3, 0] * KK1 + Ark[3, 1] * KK2 + Ark[3, 2] * KK3)
                                KK4 = -1j * SFt @ RhoMuNuIn_K

                            # 根据不同的RK阶数, 得到 Rho_fin
                            if rk == 1:
                                Rho_fin = RhoMuNuIn + Delta_tR * Brk[0, 0] * KK1
                            if rk == 2:
                                Rho_fin = RhoMuNuIn + Delta_tR * (Brk[0, 0] * KK1 + Brk[1, 0] * KK2)
                            if rk == 3:
                                Rho_fin = RhoMuNuIn + Delta_tR * (Brk[0, 0] * KK1 + Brk[1, 0] * KK2 + Brk[2, 0] * KK3)
                            if rk == 4:
                                Rho_fin = RhoMuNuIn + Delta_tR * (Brk[0, 0] * KK1 + Brk[1, 0] * KK2 +
                                                                  Brk[2, 0] * KK3 + Brk[3, 0] * KK4)

                            RhoMuNuIn = Rho_fin  # 更新

                        # 计算最终激发态布居之和
                        Pop_flu = 0
                        for jj in range(N1):
                            Pop_flu = Pop_flu + Rho_fin[jj + 1, 0] * np.conj(Rho_fin[jj + 1, 0])

                        # 加权到相位匹配
                        PopiR[itau12, itau34] = PopiR[itau12, itau34] + Pop_flu * Rm
                        PopiNR[itau12, itau34] = PopiNR[itau12, itau34] + Pop_flu * NRm

            # 将PopiR,PopiNR对应itau12行的结果存到 pppR, pppNR(仅示例, 取 itau34=1)
            # 实际需要2D存储可以参考PopiR, PopiNR本身
            pppR[itau12, 0] = np.real(PopiR[itau12, 0])
            pppNR[itau12, 0] = np.real(PopiNR[itau12, 0])

    # 将结果存到3D数组中(随T变化)
    PopiR3[:, :, iT] = PopiR[:, :]
    PopiNR3[:, :, iT] = PopiNR[:, :]

# 合并
PopiT = PopiR + PopiNR

# 保存结果
scipy.io.savemat('tau40_0.0001_Tp200_23.mat', {
    'PopiR': PopiR, 'PopiNR': PopiNR, 'PopiT': PopiT, 'ttau12': ttau12, 'ttau34': ttau34,
    'max_tau12': max_tau12, 'max_tau34': max_tau34, 'wp': wp, 'step_tau': step_tau,
    'Tp': Tp, 'maxT': maxT, 'PopiR3': PopiR3, 'PopiNR3': PopiNR3
})

# 快速plot查看
plt.figure()
plt.plot(ttau12, pppR, 'r', ttau12, pppNR, 'k')
plt.xlabel(r'\tau_{12} (fs)')
plt.ylabel('Population')
plt.title('Real part of PopiR and PopiNR')
plt.grid(True)
plt.show()

print(f'Elapsed time: {time.time() - tic:.2f} seconds')

# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# ================== 七、2D光谱计算与绘图 ==================
tic = time.time()
print('加载并计算2D光谱...')

# 加载数据
data = scipy.io.loadmat('tau40_0.0001_Tp200_23.mat')
PopiR = data['PopiR']
PopiNR = data['PopiNR']
ttau12 = data['ttau12']
ttau34 = data['ttau34']
max_tau12 = int(data['max_tau12'])
max_tau34 = int(data['max_tau34'])
wp = data['wp'].item()
step_tau = data['step_tau'].item()

# 定义常量和扫描范围
CmeV = 0.124 / 1000.
Dp = 50 * 1   # 调整可视区
Dm = 50 * 1
ExpMin = 12500 - Dm
ExpMax = 12950 + Dp
Del = (ExpMax - ExpMin) * CmeV / 2 * 1.
wp = 1.5724 * 1 + 0.1 * 0  # 对应上面设置, 这只是个示例

max_Wtau = 51 * 4  # W_tau方向的采样点
max_Wt = 51 * 4    # W_t方向的采样点
step_Wt = 2 * Del / max_Wt
Wt0 = wp * 1 - Del + 0.0
Wtau0 = wp * 1 - Del

wlam = 1239.8424121

# 高斯权因子(模拟包络衰减之类)
f2 = 4 * np.log(2)
fw12 = np.sqrt(- f2 * max_tau12**2 * step_tau**2 / np.log(0.05))
fw34 = np.sqrt(- f2 * max_tau34**2 * step_tau**2 / np.log(0.05))
C12 = f2 / fw12**2
C34 = f2 / fw34**2

# 用于存储2D结果
StauwR = np.zeros((max_tau12, max_Wt), dtype=complex)
SwwR = np.zeros((max_Wtau, max_Wt), dtype=complex)
StauwNR = np.zeros((max_tau12, max_Wt), dtype=complex)
SwwNR = np.zeros((max_Wtau, max_Wt), dtype=complex)

Wt = np.zeros((max_Wt, 1))
WtNM = np.zeros((max_Wt, 1))
Wtau = np.zeros((max_Wtau, 1))
WtauNM = np.zeros((max_Wtau, 1))

# 先对 tau34 做加权和相乘 exp(i * \omega_t * tau34)
for j1 in range(max_tau12):
    for j3w in range(max_Wt):
        Wt[j3w, 0] = Wt0 + j3w * step_Wt
        WtNM[j3w, 0] = wlam / Wt[j3w, 0]
        for j3 in range(max_tau34):
            Wind = np.exp(-C34 * ttau34[j3, 0]**2)  # 高斯窗口
            fi = 1.52 * ttau34[j3, 0] * Wt[j3w, 0]  # 相位
            StauwR[j1, j3w] = StauwR[j1, j3w] + (
                PopiR[j1, j3] * np.exp(1j * fi) * Wind *
                np.exp(1j * wp * 1.52 * (-ttau34[j3, 0] + ttau12[j1, 0]))
            )
            StauwNR[j1, j3w] = StauwNR[j1, j3w] + (
                PopiNR[j1, j3] * np.exp(1j * fi) * Wind *
                np.exp(1j * wp * 1.52 * (-ttau34[j3, 0] - ttau12[j1, 0]))
            )

# 再对 tau12 做加权和相乘 exp(i * \omega_\tau * tau12)
for j3w in range(max_Wt):
    for j1w in range(max_Wtau):
        Wtau[j1w, 0] = Wtau0 + j1w * step_Wt
        WtauNM[j1w, 0] = wlam / Wtau[j1w, 0]
        for j1 in range(max_tau12):
            Wind = np.exp(-C12 * ttau12[j1, 0]**2)
            fi = ttau12[j1, 0] * Wtau[j1w, 0] * 1.52
            SwwR[j1w, j3w] = SwwR[j1w, j3w] + StauwR[j1, j3w] * np.exp(-1j * fi) * Wind
            SwwNR[j1w, j3w] = SwwNR[j1w, j3w] + StauwNR[j1, j3w] * np.exp(1j * fi) * Wind

# 取实部并加负号(通常实验信号在2D图像中这样定义)
Sp2DreR = -np.real(SwwR)
Sp2DreNR = -np.real(SwwNR)

# 绘制R分量
plt.figure()
ax = plt.gca()
ax.set_fontsize(16)
plt.pcolormesh(Wtau[:, 0], Wt[:, 0], Sp2DreR.T, shading='auto')
plt.xlim([1.545 - Dm * CmeV, 1.598 + Dp * CmeV])
plt.ylim([1.545 - Dm * CmeV, 1.598 + Dp * CmeV])
plt.xlabel(r'\omega_{\tau} (eV)', fontsize=20)
plt.ylabel(r'\omega_{t} (eV)', fontsize=20)
plt.title('2D Spectrum: R part')
plt.colorbar()
plt.show()

# 绘制NR分量
plt.figure()
ax = plt.gca()
ax.set_fontsize(16)
plt.pcolormesh(Wtau[:, 0], Wt[:, 0], Sp2DreNR.T, shading='auto')
plt.xlim([1.545 - Dm * CmeV, 1.598 + Dp * CmeV])
plt.ylim([1.545 - Dm * CmeV, 1.598 + Dp * CmeV])
plt.xlabel(r'\omega_{\tau} (eV)', fontsize=20)
plt.ylabel(r'\omega_{t} (eV)', fontsize=20)
plt.title('2D Spectrum: NR part')
plt.colorbar()
plt.show()

# 绘制 R+NR
plt.figure()
ax = plt.gca()
ax.set_fontsize(16)
plt.pcolormesh(Wtau[:, 0], Wt[:, 0], (Sp2DreNR + Sp2DreR).T, shading='auto')
plt.xlim([1.545 - Dm * CmeV, 1.598 + Dp * CmeV])
plt.ylim([1.545 - Dm * CmeV, 1.598 + Dp * CmeV])
plt.xlabel(r'\omega_{\tau} (eV)', fontsize=20)
plt.ylabel(r'\omega_{t} (eV)', fontsize=20)
plt.title('2D Spectrum: R + NR')
plt.colorbar()
plt.show()

print(f'2D Spectrum calculation elapsed time: {time.time() - tic:.2f} seconds')